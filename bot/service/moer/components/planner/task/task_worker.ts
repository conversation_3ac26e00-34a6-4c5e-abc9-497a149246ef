import { Planner } from '../plan/planner'
import { Job, Worker } from 'bullmq'
import { RedisDB } from '../../../../../model/redis/redis'
import logger from '../../../../../model/logger/logger'
import { Config } from '../../../../../config/config'
import { getState } from '../../flow/schedule/task/baseTask'
import { ITask } from '../types'
import { getUserId } from '../../../../../config/chat_id'
import { LLMNode } from '../../flow/nodes/llm'
import RateLimiter from '../../../../../model/redis/rate_limiter'
import { AsyncLock } from '../../../../../lib/lock/lock'
import { TaskManager } from './task_manager'
import { TaskStatus } from '@prisma/client'

export class TaskWorker {
  public static start() {
    const queueName = Planner.getPlannerSOPQueueName(Config.setting.wechatConfig?.id as string)

    new Worker(queueName, async (job: Job) => {
      const data = job.data as ITask

      // 如果任务已经被 被动回复完成了不执行任务
      const task = await TaskManager.getTaskById(data.id)
      if (task && task.status === TaskStatus.DONE) {
        return
      }

      await TaskManager.updateStatus(data.id, TaskStatus.DONE)
      await TaskWorker.processTask(data.chat_id, data.description)
    }, {
      connection: RedisDB.getInstance()
    }).on('error', (err) => {
      logger.error('task_worker 发生未捕获错误', err)
    })
  }

  public static async processTask(chatId: string, description: string) {
    const userId = getUserId(chatId)


    // 频率限制，12 小时不超过 3 条
    const limiter = new RateLimiter({
      windowSize: 12 * 60 * 60,
      maxRequests: 3
    })

    const isAllowed = await limiter.isAllowed('planner_sop', chatId)
    if (!isAllowed) {
      return
    }

    const lock = new AsyncLock()
    await lock.acquire(chatId, async () => { // 防止跟对话撞车
      const state = await getState(chatId, userId)

      logger.log({ chatId: chatId }, '执行任务', description)

      // Task to ScheduleTask
      await LLMNode.invoke({
        state,
        model: 'gpt-4.1',
        useRAG: true,
        temperature: 1,
        recallMemory: true,
        chatHistoryRounds: 6,
        promptName: 'free_talk',
        noStagePrompt: true,
        dynamicPrompt: `你需要通过主动发一条消息，来完成下面的任务：
当前任务：${description}`
      })
    })
  }
}