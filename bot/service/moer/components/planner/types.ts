/**
 * Planner 触发检测的输入
 */
export interface PlannerTriggerInput {
  conversationHistory: string
  currentUserMessage: string
}

/**
 * Planner 触发检测的输出
 */
export interface PlannerTriggerOutput {
  reasoning: string
  isPlannerTrigger: boolean
}

/**
 * 计划状态枚举
 */
export enum PlanStatus {
  DOING = 'doing',
  COMPLETED = 'completed'
}

/**
 * 计划接口
 */
export interface Plan {
  id: string
  created_at: Date
  chat_id: string
  round_id: string
  overall_goal: string
  tasks: string[]
  status: string
  current_task_index: number
}

/**
 * 创建计划的输入参数
 */
export interface CreatePlanInput {
  chat_id: string
  round_id: string
  overall_goal: string
  tasks: string[]
}

/**
 * 计划详情（包含完整信息）
 * 注意：以下字段都是基于数据库字段计算得出的，不存储在数据库中
 */
export interface PlanDetail extends Plan {
  // 以下字段都是运行时计算的，不存储在数据库中
  current_task?: string        // 根据 current_task_index 和 tasks 计算得出
  remaining_tasks: string[]    // 根据 current_task_index 和 tasks 计算得出
  completed_tasks: string[]    // 根据 current_task_index 和 tasks 计算得出
  progress_percentage: number  // 根据 current_task_index 和 tasks.length 计算得出
}

/**
 * 任务接口
 */
export interface Task {
  id: string
  chat_id: string
  round_id?: string
  overall_goal: string
  description: string
  status: string
  priority: number
  created_at: Date
  completed_at?: Date
}

/**
 * 批量创建任务的输入参数
 */
export interface CreateTasksInput {
  chat_id: string
  descriptions: string[]
  overall_goal: string
  round_id?: string
}


export interface ITask {
  id: string
  chat_id: string
  round_id: string | null
  overall_goal: string
  description: string
  status: string
  priority: number
  created_at: Date
  completed_at: Date | null
}

/**
 * 新的计划结构 - 支持增删改操作
 */
export interface PlanOperations {
  toAdd: string[]
  toUpdate: Array<{
    id: string  // 对应 getActiveTasks 的 index + 1
    content: string
  }>
  toRemove: string[]  // 对应 getActiveTasks 的 index + 1
}

/**
 * 更新后的计划响应结构
 */
export interface PlanResponse {
  think: string
  plans: PlanOperations
}

